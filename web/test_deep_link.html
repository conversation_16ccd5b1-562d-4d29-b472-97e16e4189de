<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Test Deep Link - Money Mouthy</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .test-link {
            display: block;
            margin: 10px 0;
            padding: 10px;
            background: #f0f0f0;
            text-decoration: none;
            border-radius: 5px;
        }
        .test-link:hover { background: #e0e0e0; }
        .console-output {
            background: #000;
            color: #0f0;
            padding: 10px;
            font-family: monospace;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>🔗 Deep Link Testing for Money Mouthy</h1>
    <p>Use this page to test deep linking functionality. Make sure your Flutter web app is running on port 8080.</p>

    <h2>📱 Test Links (Hash Routing - Recommended for Web):</h2>
    <a href="http://localhost:8080/#/post/test-post-1" class="test-link" target="_blank">
        🔗 Test Post 1 (Hash Route) - http://localhost:8080/#/post/test-post-1
    </a>
    <a href="http://localhost:8080/#/post/sample-post-123" class="test-link" target="_blank">
        🔗 Sample Post 123 (Hash Route) - http://localhost:8080/#/post/sample-post-123
    </a>
    <a href="http://localhost:8080/#/post/demo-post-456" class="test-link" target="_blank">
        🔗 Demo Post 456 (Hash Route) - http://localhost:8080/#/post/demo-post-456
    </a>

    <h2>🌐 Alternative Formats:</h2>
    <a href="http://localhost:8080/post/direct-post-789" class="test-link" target="_blank">
        🔗 Direct Route - http://localhost:8080/post/direct-post-789
    </a>
    <a href="http://localhost:8080/?postId=query-post-101" class="test-link" target="_blank">
        🔗 Query Parameter - http://localhost:8080/?postId=query-post-101
    </a>

    <h2>📋 Testing Instructions:</h2>
    <ol>
        <li><strong>Start the app:</strong> Run <code>fvm flutter run -d web-server --web-port 8080</code></li>
        <li><strong>Click test links:</strong> Click any of the test links above</li>
        <li><strong>Expected behavior:</strong>
            <ul>
                <li>If logged in: Navigate directly to post detail screen</li>
                <li>If not logged in: Redirect to login, then to post after login</li>
                <li>If post not found: Show "Post Not Found" dialog</li>
            </ul>
        </li>
        <li><strong>Check console:</strong> Open browser DevTools (F12) to see debug messages</li>
    </ol>

    <h2>🔍 Debug Console Output:</h2>
    <div class="console-output" id="console-output">
        Console messages will appear here...
    </div>

    <h2>🧪 Manual Testing:</h2>
    <p>You can also manually test by:</p>
    <ul>
        <li>Copying any URL format above</li>
        <li>Pasting it directly in the browser address bar</li>
        <li>Pressing Enter to navigate</li>
    </ul>

    <script>
        // Capture console messages
        const originalLog = console.log;
        const consoleOutput = document.getElementById('console-output');

        console.log = function(...args) {
            originalLog.apply(console, args);
            const message = args.join(' ');
            consoleOutput.innerHTML += new Date().toLocaleTimeString() + ': ' + message + '<br>';
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        };

        console.log('Deep link test page loaded');
        console.log('Current URL: ' + window.location.href);
        console.log('Ready to test deep links!');

        // Test URL parsing
        function testUrlParsing() {
            const testUrls = [
                'http://localhost:8080/#/post/test-123',
                'http://localhost:8080/post/test-456',
                'http://localhost:8080/?postId=test-789'
            ];

            testUrls.forEach(url => {
                console.log('Testing URL: ' + url);
                // Add your URL parsing logic here if needed
            });
        }

        // Auto-test after 2 seconds
        setTimeout(() => {
            console.log('Running automatic URL parsing tests...');
            testUrlParsing();
        }, 2000);
    </script>
</body>
</html>
