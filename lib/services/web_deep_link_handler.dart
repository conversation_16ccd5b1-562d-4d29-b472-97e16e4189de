import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../services/post_service.dart';
import '../screens/post_detail_screen.dart';
import '../screens/login.dart';
import '../screens/main_navigation_screen.dart';
import 'package:firebase_auth/firebase_auth.dart';

/// Web-specific deep link handler
class WebDeepLinkHandler {
  static final WebDeepLinkHandler _instance = WebDeepLinkHandler._internal();
  factory WebDeepLinkHandler() => _instance;
  WebDeepLinkHandler._internal();

  final PostService _postService = PostService();
  String? _pendingPostId;

  /// Initialize web deep link handling
  void initialize() {
    if (!kIsWeb) return;

    // Handle initial URL on app load
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _handleCurrentUrl();
    });
  }

  /// Handle the current browser URL
  void _handleCurrentUrl() {
    try {
      // Get current URL from browser
      final currentUrl = Uri.base.toString();
      debugPrint('Current web URL: $currentUrl');
      
      final postId = _extractPostIdFromUrl(currentUrl);
      if (postId != null && postId.isNotEmpty) {
        debugPrint('Found post ID in URL: $postId');
        _handlePostNavigation(postId);
      }
    } catch (e) {
      debugPrint('Error handling current URL: $e');
    }
  }

  /// Extract post ID from various URL formats
  String? _extractPostIdFromUrl(String url) {
    try {
      final uri = Uri.parse(url);
      
      // Check for /#/post/POST_ID format (hash routing)
      if (uri.fragment.isNotEmpty) {
        final fragment = uri.fragment;
        if (fragment.startsWith('/post/')) {
          return fragment.replaceFirst('/post/', '');
        }
      }
      
      // Check for /post/POST_ID format (direct routing)
      if (uri.pathSegments.length >= 2 && uri.pathSegments[0] == 'post') {
        return uri.pathSegments[1];
      }
      
      // Check for query parameters
      final postId = uri.queryParameters['postId'] ?? uri.queryParameters['post_id'];
      if (postId != null && postId.isNotEmpty) {
        return postId;
      }
      
      return null;
    } catch (e) {
      debugPrint('Error extracting post ID from URL: $e');
      return null;
    }
  }

  /// Handle navigation to a specific post
  Future<void> _handlePostNavigation(String postId) async {
    try {
      // Check if user is authenticated
      final user = FirebaseAuth.instance.currentUser;
      
      if (user == null || !user.emailVerified) {
        // Store post ID for after-login navigation
        _pendingPostId = postId;
        debugPrint('User not authenticated, storing post ID: $_pendingPostId');
        Get.offAllNamed('/login');
        return;
      }

      // User is authenticated, navigate to post
      await _navigateToPost(postId);
      
    } catch (e) {
      debugPrint('Error handling post navigation: $e');
      _showPostNotFoundDialog();
    }
  }

  /// Navigate to post detail screen
  Future<void> _navigateToPost(String postId) async {
    try {
      debugPrint('Navigating to post: $postId');

      // Initialize PostService
      await _postService.initialize();

      // Find the post
      final posts = _postService.getAllPosts();
      final post = posts.firstWhereOrNull((p) => p.id == postId);

      if (post != null) {
        // Navigate to post detail screen
        Get.to(
          () => PostDetailScreen(post: post),
          transition: Transition.cupertino,
          duration: const Duration(milliseconds: 300),
        );
      } else {
        // Post not found
        debugPrint('Post not found: $postId');
        _showPostNotFoundDialog();
      }
    } catch (e) {
      debugPrint('Error navigating to post: $e');
      _showPostNotFoundDialog();
    }
  }

  /// Show post not found dialog
  void _showPostNotFoundDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('Post Not Found'),
        content: const Text(
          'The post you\'re looking for could not be found. It may have been deleted or is no longer available.',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Get.back(); // Close dialog
              Get.offAllNamed('/home');
            },
            child: const Text('Go to Home'),
          ),
        ],
      ),
    );
  }

  /// Handle post-login navigation if there's a pending post
  Future<void> handlePostLoginNavigation() async {
    if (_pendingPostId != null) {
      final postId = _pendingPostId!;
      _pendingPostId = null; // Clear pending post ID
      
      debugPrint('Handling post-login navigation to post: $postId');
      
      // Small delay to ensure the main screen is loaded
      await Future.delayed(const Duration(milliseconds: 500));
      
      await _navigateToPost(postId);
    }
  }

  /// Check if there's a pending post navigation
  bool get hasPendingNavigation => _pendingPostId != null;

  /// Get pending post ID
  String? get pendingPostId => _pendingPostId;

  /// Clear pending navigation
  void clearPendingNavigation() {
    _pendingPostId = null;
  }

  /// Generate web-friendly URL for sharing
  String generateWebUrl(String postId) {
    if (kIsWeb) {
      final currentUri = Uri.base;
      return '${currentUri.scheme}://${currentUri.host}${currentUri.port != 80 && currentUri.port != 443 ? ':${currentUri.port}' : ''}/#/post/$postId';
    }
    return 'https://moneymouthy.com/post/$postId';
  }

  /// Test deep link functionality
  void testDeepLink(String postId) {
    if (!kIsWeb) return;
    
    debugPrint('Testing deep link for post: $postId');
    _handlePostNavigation(postId);
  }
}
