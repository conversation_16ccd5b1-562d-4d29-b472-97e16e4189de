import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:share_plus/share_plus.dart';
import '../services/post_service.dart';
import '../services/deep_link_handler.dart';

/// Service to handle post sharing functionality
class PostSharingService {
  static final PostSharingService _instance = PostSharingService._internal();
  factory PostSharingService() => _instance;
  PostSharingService._internal();

  final DeepLinkHandler _deepLinkHandler = DeepLinkHandler();

  /// Share a post to external apps
  Future<void> sharePost(
    Post post, {
    BuildContext? context,
    Rect? sharePositionOrigin,
  }) async {
    try {
      // Generate shareable URL
      final shareUrl = _deepLinkHandler.generatePostUrl(post.id);
      
      // Create share content
      final shareContent = _buildShareContent(post, shareUrl);
      
      // Show loading indicator
      if (context != null) {
        _showSharingFeedback(context);
      }

      // Share using the share_plus package
      final result = await Share.shareWithResult(
        shareContent.text,
        subject: shareContent.subject,
        sharePositionOrigin: sharePositionOrigin,
      );

      // Handle share result
      _handleShareResult(result, context);

    } catch (e) {
      debugPrint('Error sharing post: $e');
      if (context != null) {
        _showErrorSnackBar(context, 'Failed to share post. Please try again.');
      }
    }
  }

  /// Copy post link to clipboard
  Future<void> copyPostLink(Post post, BuildContext? context) async {
    try {
      final shareUrl = _deepLinkHandler.generatePostUrl(post.id);
      
      await Clipboard.setData(ClipboardData(text: shareUrl));
      
      if (context != null) {
        _showSuccessSnackBar(context, 'Link copied to clipboard!');
      }
    } catch (e) {
      debugPrint('Error copying post link: $e');
      if (context != null) {
        _showErrorSnackBar(context, 'Failed to copy link. Please try again.');
      }
    }
  }

  /// Share post with custom message
  Future<void> sharePostWithMessage(
    Post post,
    String customMessage, {
    BuildContext? context,
    Rect? sharePositionOrigin,
  }) async {
    try {
      final shareUrl = _deepLinkHandler.generatePostUrl(post.id);
      
      final shareText = '$customMessage\n\n$shareUrl';
      
      if (context != null) {
        _showSharingFeedback(context);
      }

      final result = await Share.shareWithResult(
        shareText,
        subject: _getPostTitle(post),
        sharePositionOrigin: sharePositionOrigin,
      );

      _handleShareResult(result, context);

    } catch (e) {
      debugPrint('Error sharing post with custom message: $e');
      if (context != null) {
        _showErrorSnackBar(context, 'Failed to share post. Please try again.');
      }
    }
  }

  /// Build share content for a post
  ShareContent _buildShareContent(Post post, String shareUrl) {
    final title = _getPostTitle(post);
    final preview = _getPostPreview(post.content);
    final author = post.author;
    
    // Create engaging share text
    String shareText;
    if (post.hasPoll) {
      shareText = '🗳️ Check out this poll by $author on Money Mouthy!\n\n'
          '"$preview"\n\n'
          'Cast your vote and see what others think!\n\n'
          '$shareUrl';
    } else if (post.price > 0) {
      shareText = '💰 Exclusive content by $author on Money Mouthy!\n\n'
          '"$preview"\n\n'
          'Unlock this premium post for just \$${post.price.toStringAsFixed(2)}\n\n'
          '$shareUrl';
    } else {
      shareText = '💬 Check out this post by $author on Money Mouthy!\n\n'
          '"$preview"\n\n'
          'Join the conversation!\n\n'
          '$shareUrl';
    }

    return ShareContent(
      text: shareText,
      subject: 'Money Mouthy: $title',
    );
  }

  /// Get post title or generate one from content
  String _getPostTitle(Post post) {
    if (post.title != null && post.title!.isNotEmpty) {
      return post.title!;
    }
    
    // Generate title from content
    final words = post.content.split(' ').take(6).join(' ');
    return words.length < post.content.length ? '$words...' : words;
  }

  /// Get post content preview
  String _getPostPreview(String content) {
    const maxLength = 120;
    if (content.length <= maxLength) {
      return content;
    }
    
    // Find the last complete word within the limit
    final truncated = content.substring(0, maxLength);
    final lastSpace = truncated.lastIndexOf(' ');
    
    if (lastSpace > maxLength * 0.8) {
      return '${content.substring(0, lastSpace)}...';
    }
    
    return '${truncated}...';
  }

  /// Show sharing feedback to user
  void _showSharingFeedback(BuildContext context) {
    // Provide haptic feedback
    HapticFeedback.lightImpact();
    
    // You could show a brief loading indicator here if needed
    // For now, we'll just provide haptic feedback
  }

  /// Handle share result
  void _handleShareResult(ShareResult result, BuildContext? context) {
    if (context == null) return;

    switch (result.status) {
      case ShareResultStatus.success:
        _showSuccessSnackBar(context, 'Post shared successfully!');
        break;
      case ShareResultStatus.dismissed:
        // User dismissed the share dialog, no action needed
        break;
      case ShareResultStatus.unavailable:
        _showErrorSnackBar(context, 'Sharing is not available on this device.');
        break;
    }
  }

  /// Show success snack bar
  void _showSuccessSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  /// Show error snack bar
  void _showErrorSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error_outline, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  /// Show share options bottom sheet
  void showShareOptions(
    BuildContext context,
    Post post, {
    Rect? sharePositionOrigin,
  }) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => ShareOptionsBottomSheet(
        post: post,
        onShare: () => sharePost(post, context: context, sharePositionOrigin: sharePositionOrigin),
        onCopyLink: () => copyPostLink(post, context),
      ),
    );
  }
}

/// Data class for share content
class ShareContent {
  final String text;
  final String subject;

  const ShareContent({
    required this.text,
    required this.subject,
  });
}

/// Bottom sheet widget for share options
class ShareOptionsBottomSheet extends StatelessWidget {
  final Post post;
  final VoidCallback onShare;
  final VoidCallback onCopyLink;

  const ShareOptionsBottomSheet({
    super.key,
    required this.post,
    required this.onShare,
    required this.onCopyLink,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 20),
          
          // Title
          Text(
            'Share Post',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 20),
          
          // Share options
          ListTile(
            leading: const Icon(Icons.share, color: Colors.blue),
            title: const Text('Share to Apps'),
            subtitle: const Text('Share via WhatsApp, Telegram, etc.'),
            onTap: () {
              Navigator.pop(context);
              onShare();
            },
          ),
          
          ListTile(
            leading: const Icon(Icons.link, color: Colors.green),
            title: const Text('Copy Link'),
            subtitle: const Text('Copy link to clipboard'),
            onTap: () {
              Navigator.pop(context);
              onCopyLink();
            },
          ),
          
          const SizedBox(height: 10),
        ],
      ),
    );
  }
}
