import 'package:flutter/foundation.dart';

/// Service for generating shareable URLs with platform-specific optimizations
class UrlGenerationService {
  static final UrlGenerationService _instance =
      UrlGenerationService._internal();
  factory UrlGenerationService() => _instance;
  UrlGenerationService._internal();

  // Configuration for different environments
  static const String _productionDomain = 'moneymouthy.com';
  static const String _developmentDomain = 'localhost:3000';
  static const String _customScheme = 'moneymouthy';

  /// Generate a shareable URL for a post
  String generatePostUrl(
    String postId, {
    bool preferCustomScheme = false,
    bool forceHttps = true,
    Map<String, String>? additionalParams,
  }) {
    try {
      // Validate post ID
      if (postId.isEmpty) {
        throw ArgumentError('Post ID cannot be empty');
      }

      // Choose URL format based on platform and preferences
      if (preferCustomScheme && !kIsWeb) {
        return _generateCustomSchemeUrl(postId, additionalParams);
      } else {
        return _generateWebUrl(postId, forceHttps, additionalParams);
      }
    } catch (e) {
      debugPrint('Error generating post URL: $e');
      // Fallback to basic web URL
      return _generateFallbackUrl(postId);
    }
  }

  /// Generate custom scheme URL for mobile apps
  String _generateCustomSchemeUrl(
    String postId,
    Map<String, String>? additionalParams,
  ) {
    final uri = Uri(
      scheme: _customScheme,
      path: '/post/$postId',
      queryParameters: additionalParams,
    );
    return uri.toString();
  }

  /// Generate web URL with proper domain
  String _generateWebUrl(
    String postId,
    bool forceHttps,
    Map<String, String>? additionalParams,
  ) {
    final domain = _getDomain();
    final scheme = forceHttps ? 'https' : 'http';

    final uri = Uri(
      scheme: scheme,
      host: domain,
      path: '/post/$postId',
      queryParameters: additionalParams,
    );
    return uri.toString();
  }

  /// Generate fallback URL for error cases
  String _generateFallbackUrl(String postId) {
    return 'https://$_productionDomain/post/$postId';
  }

  /// Get appropriate domain based on environment
  String _getDomain() {
    if (kDebugMode && kIsWeb) {
      // For web development, use the current host
      try {
        final currentHost = Uri.base.host;
        final currentPort = Uri.base.port;
        if (currentHost == 'localhost' || currentHost == '127.0.0.1') {
          return currentPort != 80 && currentPort != 443
              ? '$currentHost:$currentPort'
              : currentHost;
        }
      } catch (e) {
        debugPrint('Error getting current host: $e');
      }
    }
    return _productionDomain;
  }

  /// Generate URL with tracking parameters
  String generatePostUrlWithTracking(
    String postId, {
    String? source,
    String? medium,
    String? campaign,
    bool preferCustomScheme = false,
  }) {
    final trackingParams = <String, String>{};

    if (source != null) trackingParams['utm_source'] = source;
    if (medium != null) trackingParams['utm_medium'] = medium;
    if (campaign != null) trackingParams['utm_campaign'] = campaign;

    // Add app-specific tracking
    trackingParams['shared_from'] = 'mobile_app';
    trackingParams['timestamp'] =
        DateTime.now().millisecondsSinceEpoch.toString();

    return generatePostUrl(
      postId,
      preferCustomScheme: preferCustomScheme,
      additionalParams: trackingParams,
    );
  }

  /// Generate URL optimized for specific social platforms
  String generateSocialOptimizedUrl(
    String postId,
    SocialPlatform platform, {
    Map<String, String>? customParams,
  }) {
    final params = <String, String>{
      'utm_source': platform.name.toLowerCase(),
      'utm_medium': 'social',
      'utm_campaign': 'post_share',
      ...?customParams,
    };

    // Platform-specific optimizations
    switch (platform) {
      case SocialPlatform.whatsapp:
        params['utm_content'] = 'whatsapp_share';
        break;
      case SocialPlatform.telegram:
        params['utm_content'] = 'telegram_share';
        break;
      case SocialPlatform.twitter:
        params['utm_content'] = 'twitter_share';
        break;
      case SocialPlatform.facebook:
        params['utm_content'] = 'facebook_share';
        break;
      case SocialPlatform.instagram:
        params['utm_content'] = 'instagram_share';
        break;
      case SocialPlatform.linkedin:
        params['utm_content'] = 'linkedin_share';
        break;
      case SocialPlatform.email:
        params['utm_content'] = 'email_share';
        break;
      case SocialPlatform.sms:
        params['utm_content'] = 'sms_share';
        break;
      case SocialPlatform.other:
        params['utm_content'] = 'other_share';
        break;
    }

    return generatePostUrl(
      postId,
      additionalParams: params,
    );
  }

  /// Generate short URL (placeholder for future URL shortening service)
  Future<String> generateShortUrl(String postId) async {
    // TODO: Implement URL shortening service integration
    // For now, return the regular URL
    return generatePostUrl(postId);
  }

  /// Validate if a URL is a valid Money Mouthy post URL
  bool isValidPostUrl(String url) {
    try {
      final uri = Uri.parse(url);

      // Check custom scheme
      if (uri.scheme == _customScheme) {
        return uri.pathSegments.length >= 2 &&
            uri.pathSegments[0] == 'post' &&
            uri.pathSegments[1].isNotEmpty;
      }

      // Check web URL
      if ((uri.scheme == 'http' || uri.scheme == 'https') &&
          (uri.host == _productionDomain || uri.host == _developmentDomain)) {
        return uri.pathSegments.length >= 2 &&
            uri.pathSegments[0] == 'post' &&
            uri.pathSegments[1].isNotEmpty;
      }

      return false;
    } catch (e) {
      return false;
    }
  }

  /// Extract post ID from a Money Mouthy URL
  String? extractPostIdFromUrl(String url) {
    try {
      final uri = Uri.parse(url);

      if (uri.pathSegments.length >= 2 && uri.pathSegments[0] == 'post') {
        return uri.pathSegments[1];
      }

      // Check query parameters as fallback
      return uri.queryParameters['postId'] ?? uri.queryParameters['post_id'];
    } catch (e) {
      debugPrint('Error extracting post ID from URL: $e');
      return null;
    }
  }

  /// Generate QR code URL for a post (for future QR code feature)
  String generateQrCodeUrl(String postId) {
    final postUrl = generatePostUrl(postId);
    // TODO: Integrate with QR code generation service
    return 'https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${Uri.encodeComponent(postUrl)}';
  }

  /// Generate preview URL for social media meta tags
  String generatePreviewUrl(String postId) {
    return generatePostUrl(
      postId,
      additionalParams: {'preview': 'true'},
    );
  }
}

/// Supported social platforms for URL optimization
enum SocialPlatform {
  whatsapp,
  telegram,
  twitter,
  facebook,
  instagram,
  linkedin,
  email,
  sms,
  other,
}
