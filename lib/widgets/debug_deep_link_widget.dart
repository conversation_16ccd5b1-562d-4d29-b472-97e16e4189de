import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/web_deep_link_handler.dart';
import '../services/post_service.dart';

/// Debug widget for testing deep links (only shows in debug mode)
class DebugDeepLinkWidget extends StatefulWidget {
  const DebugDeepLinkWidget({super.key});

  @override
  State<DebugDeepLinkWidget> createState() => _DebugDeepLinkWidgetState();
}

class _DebugDeepLinkWidgetState extends State<DebugDeepLinkWidget> {
  final PostService _postService = PostService();
  final WebDeepLinkHandler _webHandler = WebDeepLinkHandler();
  List<Post> _posts = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    if (kDebugMode && kIsWeb) {
      _loadPosts();
    }
  }

  Future<void> _loadPosts() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      await _postService.initialize();
      final posts = _postService.getAllPosts();

      if (mounted) {
        setState(() {
          _posts = posts.take(5).toList(); // Show first 5 posts
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Error loading posts for debug: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _copyToClipboard(String text) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Copied to clipboard: $text'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _testDeepLink(String postId) {
    final url = _webHandler.generateWebUrl(postId);
    debugPrint('Testing deep link: $url');
    _webHandler.testDeepLink(postId);
  }

  @override
  Widget build(BuildContext context) {
    // Only show in debug mode on web
    if (!kDebugMode || !kIsWeb) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.orange.withOpacity(0.1),
        border: Border.all(color: Colors.orange),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              const Icon(Icons.bug_report, color: Colors.orange),
              const SizedBox(width: 8),
              const Text(
                'DEBUG: Deep Link Testing',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.orange,
                ),
              ),
              const Spacer(),
              IconButton(
                icon: const Icon(Icons.refresh),
                onPressed: _loadPosts,
                tooltip: 'Refresh posts',
              ),
            ],
          ),
          const SizedBox(height: 8),
          if (_isLoading)
            const Center(child: CircularProgressIndicator())
          else if (_posts.isEmpty)
            const Text('No posts found. Create some posts first!')
          else ...[
            const Text('Click to test deep links with real post IDs:'),
            const SizedBox(height: 8),
            ..._posts.map((post) {
              final url = _webHandler.generateWebUrl(post.id);
              final title = post.title?.isNotEmpty == true
                  ? post.title!
                  : post.content.length > 30
                      ? '${post.content.substring(0, 30)}...'
                      : post.content;

              return Card(
                margin: const EdgeInsets.symmetric(vertical: 4),
                child: ListTile(
                  dense: true,
                  title: Text(
                    title,
                    style: const TextStyle(fontSize: 12),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  subtitle: Text(
                    'ID: ${post.id}',
                    style:
                        const TextStyle(fontSize: 10, fontFamily: 'monospace'),
                  ),
                  trailing: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        icon: const Icon(Icons.copy, size: 16),
                        onPressed: () => _copyToClipboard(url),
                        tooltip: 'Copy URL',
                      ),
                      IconButton(
                        icon: const Icon(Icons.launch, size: 16),
                        onPressed: () => _testDeepLink(post.id),
                        tooltip: 'Test deep link',
                      ),
                    ],
                  ),
                ),
              );
            }).toList(),
            const SizedBox(height: 8),
            Row(
              children: [
                ElevatedButton.icon(
                  onPressed: () => _webHandler.listAvailablePostIds(),
                  icon: const Icon(Icons.list, size: 16),
                  label: const Text('Log All Post IDs'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    foregroundColor: Colors.white,
                  ),
                ),
                const SizedBox(width: 8),
                ElevatedButton.icon(
                  onPressed: () {
                    const testUrl = 'http://localhost:8080/#/post/test-123';
                    _copyToClipboard(testUrl);
                  },
                  icon: const Icon(Icons.link, size: 16),
                  label: const Text('Copy Test URL'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ],
          const SizedBox(height: 8),
          const Text(
            'Note: This debug panel only appears in debug mode on web.',
            style: TextStyle(fontSize: 10, color: Colors.grey),
          ),
        ],
      ),
    );
  }
}
