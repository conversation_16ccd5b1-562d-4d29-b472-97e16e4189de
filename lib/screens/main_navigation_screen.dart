import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:firebase_auth/firebase_auth.dart';

import '../controllers/category_controller.dart';
import 'create_post.dart';
import 'search_screen.dart';
import 'profile_screen.dart';
import 'wallet_screen.dart';
import '../widgets/home/<USER>';
import 'package:get/get.dart';
import '../controllers/wallet_controller.dart';
import '../controllers/profile_controller.dart';
import '../controllers/draft_controller.dart';
import '../controllers/poll_controller.dart';
import '../services/payments/payment_redirect_handler.dart';
import '../services/deep_link_handler.dart';
import '../services/web_deep_link_handler.dart';
import '../widgets/profile_drawer.dart';

class MainNavigationScreen extends StatefulWidget {
  const MainNavigationScreen({super.key});

  @override
  State<MainNavigationScreen> createState() => _MainNavigationScreenState();
}

class _MainNavigationScreenState extends State<MainNavigationScreen> {
  int _currentIndex = 0;
  late List<Widget> _screens;

  // Double back to exit functionality
  DateTime? _lastBackPressed;

  @override
  void initState() {
    super.initState();
    _initializeServices();
    _initializeScreens();
    _handlePaymentRedirect();
    _handleDeepLinkNavigation();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Also check for payment redirects when dependencies change
    // This helps catch cases where the URL wasn't cleaned properly
    _handlePaymentRedirect();
  }

  void _handlePaymentRedirect() {
    if (kIsWeb && PaymentRedirectHandler.hasPaymentRedirect()) {
      // Handle payment redirect after the widget is built
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        final result =
            await PaymentRedirectHandler.handlePaymentRedirect(context);
        if (mounted) {
          PaymentRedirectHandler.showPaymentResult(context, result!);
        }
      });
    }
  }

  void _handleDeepLinkNavigation() {
    // Handle pending deep link navigation after login
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      if (kIsWeb) {
        final webDeepLinkHandler = WebDeepLinkHandler();
        if (webDeepLinkHandler.hasPendingNavigation) {
          await webDeepLinkHandler.handlePostLoginNavigation();
        }
      } else {
        final deepLinkHandler = DeepLinkHandler();
        if (deepLinkHandler.hasPendingNavigation) {
          await deepLinkHandler.handlePostLoginNavigation();
        }
      }
    });
  }

  void _initializeScreens() {
    final currentUid = FirebaseAuth.instance.currentUser?.uid ?? '';
    _screens = [
      const HomeContent(), // Home content without bottom nav
      const WalletScreen(), // Wallet tab
      const CreatePostScreen(),
      const SearchScreen(),
      ProfileScreen(userId: currentUid),
    ];
  }

  Future<void> _initializeServices() async {
    try {
      // Initialize authenticated controllers only after user is logged in

      Get.put(CategoryController(), permanent: true);
      // Get.put(PostController(), permanent: true);
      Get.put(WalletController(), permanent: true);
      Get.put(DraftController(), permanent: true);
      Get.put(PollController(), permanent: true);

      // ProfileController is already initialized in main.dart
      // Initialize user data and wallet
      await Get.find<ProfileController>().initializeCurrentUser();
      await Get.find<WalletController>().initialize();

      debugPrint('MainNavigationScreen: Authenticated controllers initialized');
    } catch (e) {
      debugPrint('Error initializing services: $e');
    }
  }

  void _onTabTapped(int index) {
    if (_currentIndex != index) {
      HapticFeedback.selectionClick();
      setState(() {
        _currentIndex = index;
      });
    }
  }

  Future<bool> _onWillPop() async {
    final now = DateTime.now();
    const backPressDuration = Duration(seconds: 2);

    if (_lastBackPressed == null ||
        now.difference(_lastBackPressed!) > backPressDuration) {
      _lastBackPressed = now;

      // Show snackbar with exit message
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Press back again to exit'),
          duration: Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
        ),
      );
      return false; // Don't exit
    }

    return true; // Exit the app
  }

  Widget _buildCompactDrawer() {
    return const ProfileDrawer(isMobileCompact: true);
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) return;
        final shouldExit = await _onWillPop();
        if (shouldExit && mounted) {
          // Exit the app
          SystemNavigator.pop();
        }
      },
      child: LayoutBuilder(
        builder: (context, constraints) {
          final isMobile = constraints.maxWidth < 768;

          return Scaffold(
            body: Center(
              child: Container(
                constraints: BoxConstraints(
                  maxWidth: kIsWeb ? 800 : double.infinity,
                ),
                child: Row(
                  children: [
                    // Permanent Drawer - always visible
                    Container(
                      width: isMobile ? 100 : 230,
                      decoration: BoxDecoration(
                        color: Colors.grey.shade50,
                        border: Border(
                          right: BorderSide(color: Colors.grey.shade200),
                        ),
                      ),
                      child: isMobile
                          ? _buildCompactDrawer()
                          : const ProfileDrawer(),
                    ),
                    // Main Content
                    Expanded(
                      child: Column(
                        children: [
                          // Main screen content
                          Expanded(
                            child: AnimatedSwitcher(
                              duration: const Duration(milliseconds: 250),
                              transitionBuilder: (child, animation) {
                                return SlideTransition(
                                  position: Tween<Offset>(
                                    begin: const Offset(0.05, 0),
                                    end: Offset.zero,
                                  ).animate(
                                    CurvedAnimation(
                                      parent: animation,
                                      curve: Curves.easeInOutCubic,
                                    ),
                                  ),
                                  child: FadeTransition(
                                    opacity: animation,
                                    child: child,
                                  ),
                                );
                              },
                              child: IndexedStack(
                                key: ValueKey(_currentIndex),
                                index: _currentIndex,
                                children: _screens,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            bottomNavigationBar: Center(
              heightFactor: kIsWeb ? 1.0 : 0.8,
              child: Container(
                constraints: BoxConstraints(
                  maxWidth: kIsWeb ? 800 : double.infinity,
                ),
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  curve: Curves.easeInOut,
                  child: BottomNavigationBar(
                    type: BottomNavigationBarType.fixed,
                    selectedItemColor: const Color(0xFF4C5DFF),
                    unselectedItemColor: Colors.grey,
                    currentIndex: _currentIndex,
                    onTap: _onTabTapped,
                    elevation: 8,
                    backgroundColor: Colors.white,
                    items: const [
                      BottomNavigationBarItem(
                        icon: Icon(Icons.home_outlined),
                        label: '',
                      ),
                      BottomNavigationBarItem(
                        icon: Icon(Icons.account_balance_wallet_outlined),
                        label: '',
                      ),
                      BottomNavigationBarItem(
                        icon: Icon(
                          Icons.add_box_outlined,
                          size: 33,
                        ),
                        label: '',
                      ),
                      BottomNavigationBarItem(
                        icon: Icon(Icons.search_outlined),
                        label: '',
                      ),
                      BottomNavigationBarItem(
                        icon: Icon(Icons.person_outlined),
                        label: '',
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
